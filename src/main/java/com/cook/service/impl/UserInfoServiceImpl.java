package com.cook.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cook.common.util.JwtUtil;
//import com.cook.common.util.RedisUtil;
import com.cook.model.base.ResultData;
import com.cook.model.dto.WxLoginDto;
import com.cook.model.dto.WxSessionResult;
import com.cook.model.entity.UserInfo;
import com.cook.model.mapper.UserInfoRepository;
import com.cook.model.vo.LoginResultVo;
import com.cook.model.vo.UserInfoVo;
import com.cook.service.UserInfoService;
import com.cook.service.WxApiService;
import com.cook.service.client.WxServiceClient;
import org.springframework.web.client.RestTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 用户信息服务实现类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = { @Autowired })
public class UserInfoServiceImpl implements UserInfoService {

    final WxServiceClient wxServiceClient;
    final WxApiService wxApiService;
    final UserInfoRepository userInfoRepository;
    final JwtUtil jwtUtil;
    final RestTemplate restTemplate;
//    final RedisUtil redisUtil;

    @Value("${wx.miniapp.appid:}")
    private String wxAppid;

    @Value("${wx.miniapp.secret:}")
    private String wxSecret;

    @Value("${wx.miniapp.use-feign:false}")
    private Boolean useFeign;

    @Override
    public ResultData<LoginResultVo> wxLogin(WxLoginDto dto) {
        log.info("微信小程序登录，参数：{}", JSONUtil.toJsonStr(dto));
        log.info("当前配置 useFeign: {}", useFeign);

        try {
            // 1. 调用微信接口获取openid和session_key
            WxSessionResult sessionResult;

            // 临时强制使用RestTemplate，避免Feign的text/plain问题
            log.info("使用RestTemplate直接调用微信API（临时强制）");
            sessionResult = callWxApiDirectly(wxAppid, wxSecret, dto.getCode(), "authorization_code");

            // 原来的条件判断逻辑（暂时注释）
            /*
             * if (useFeign) {
             * log.info("使用Feign客户端调用微信API");
             * sessionResult = wxServiceClient.code2Session(
             * wxAppid, wxSecret, dto.getCode(), "authorization_code");
             * } else {
             * log.info("使用RestTemplate调用微信API");
             * sessionResult = wxApiService.code2Session(
             * wxAppid, wxSecret, dto.getCode(), "authorization_code");
             * }
             */

            log.info("微信code2session结果：{}", JSONUtil.toJsonStr(sessionResult));

            // 2. 检查微信接口调用是否成功
            if (sessionResult.getErrcode() != null && sessionResult.getErrcode() != 0) {
                return ResultData.fail("微信登录失败：" + sessionResult.getErrmsg());
            }

            if (StrUtil.isBlank(sessionResult.getOpenid())) {
                return ResultData.fail("获取用户openid失败");
            }

            // 3. 查询或创建用户
            UserInfo userInfo = getOrCreateUser(sessionResult, dto.getUserInfo());

            // 4. 生成JWT token
            String token = jwtUtil.generateToken(userInfo.getId(), userInfo.getOpenid(), userInfo.getNickname());
            Long expireTime = System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L; // 7天过期

            // 5. 构建返回结果
            UserInfoVo userInfoVo = new UserInfoVo();
            BeanUtils.copyProperties(userInfo, userInfoVo);

            // 6. 将用户信息存储到Redis缓存中（如果Redis可用）
//            try {
//                String userInfoJson = JSONUtil.toJsonStr(userInfoVo);
//                redisUtil.setUserLoginStatus(userInfo.getId(), userInfoJson, 7 * 24 * 60 * 60); // 7天过期
//                log.info("用户{}登录状态已缓存到Redis", userInfo.getId());
//            } catch (Exception e) {
//                log.warn("缓存用户{}登录状态到Redis失败，但不影响登录流程", userInfo.getId(), e);
//            }

            LoginResultVo loginResult = LoginResultVo.builder()
                    .userId(userInfo.getId())
                    .token(token)
                    .openid(sessionResult.getOpenid())
                    .sessionKey(sessionResult.getSessionKey())
                    .unionid(sessionResult.getUnionid())
                    .expireTime(expireTime)
                    .userInfo(userInfoVo)
                    .build();

            return ResultData.success(loginResult);

        } catch (Exception e) {
            log.error("微信登录异常", e);
            return ResultData.fail("登录失败，请稍后重试");
        }
    }

    @Override
    public ResultData<UserInfoVo> getUserInfo(Long userId) {
        UserInfo userInfo = userInfoRepository.getById(userId);
        if (userInfo == null) {
            return ResultData.fail("用户不存在");
        }

        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(userInfo, userInfoVo);

        return ResultData.success(userInfoVo);
    }

    @Override
    public ResultData<LoginResultVo> refreshToken(Long userId) {
        UserInfo userInfo = userInfoRepository.getById(userId);
        if (userInfo == null) {
            return ResultData.fail("用户不存在");
        }

        // 生成新JWT token
        String token = jwtUtil.generateToken(userInfo.getId(), userInfo.getOpenid(), userInfo.getNickname());
        Long expireTime = System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L;

        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(userInfo, userInfoVo);

        LoginResultVo loginResult = LoginResultVo.builder()
                .userId(userId)
                .token(token)
                .expireTime(expireTime)
                .userInfo(userInfoVo)
                .build();

        return ResultData.success(loginResult);
    }

    /**
     * 获取或创建用户
     */
    private UserInfo getOrCreateUser(WxSessionResult sessionResult, WxLoginDto.UserInfoDto userInfoDto) {
        // 根据openid查询用户
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getOpenid, sessionResult.getOpenid());
        UserInfo existUser = userInfoRepository.getOne(queryWrapper);

        Date now = new Date();

        if (existUser != null) {
            // 用户已存在，更新最后登录时间和用户信息
            existUser.setLastLoginTime(now);
            existUser.setUpdateTime(now);

            // 如果传入了用户信息，则更新
            if (userInfoDto != null) {
                updateUserInfo(existUser, userInfoDto);
            }

            userInfoRepository.updateById(existUser);
            return existUser;
        } else {
            // 创建新用户
            UserInfo newUser = UserInfo.builder()
                    .openid(sessionResult.getOpenid())
                    .unionid(sessionResult.getUnionid())
                    .status(0) // 正常状态
                    .lastLoginTime(now)
                    .createTime(now)
                    .updateTime(now)
                    .build();

            // 如果传入了用户信息，则设置
            if (userInfoDto != null) {
                updateUserInfo(newUser, userInfoDto);
            }

            userInfoRepository.save(newUser);
            return newUser;
        }
    }

    /**
     * 更新用户信息
     */
    private void updateUserInfo(UserInfo userInfo, WxLoginDto.UserInfoDto userInfoDto) {
        if (StrUtil.isNotBlank(userInfoDto.getNickName())) {
            userInfo.setNickname(userInfoDto.getNickName());
        }
        if (StrUtil.isNotBlank(userInfoDto.getAvatarUrl())) {
            userInfo.setAvatarUrl(userInfoDto.getAvatarUrl());
        }
        if (userInfoDto.getGender() != null) {
            userInfo.setGender(userInfoDto.getGender());
        }
        if (StrUtil.isNotBlank(userInfoDto.getCountry())) {
            userInfo.setCountry(userInfoDto.getCountry());
        }
        if (StrUtil.isNotBlank(userInfoDto.getProvince())) {
            userInfo.setProvince(userInfoDto.getProvince());
        }
        if (StrUtil.isNotBlank(userInfoDto.getCity())) {
            userInfo.setCity(userInfoDto.getCity());
        }
        if (StrUtil.isNotBlank(userInfoDto.getLanguage())) {
            userInfo.setLanguage(userInfoDto.getLanguage());
        }
    }

    /**
     * 直接调用微信API，避免Feign的text/plain问题
     */
    private WxSessionResult callWxApiDirectly(String appid, String secret, String jsCode, String grantType) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={jsCode}&grant_type={grantType}";

        log.info("直接调用微信API，URL: {}", url);
        log.info("参数 - appid: {}, jsCode: {}, grantType: {}", appid, jsCode, grantType);

        try {
            WxSessionResult result = restTemplate.getForObject(url, WxSessionResult.class, appid, secret, jsCode,
                    grantType);
            log.info("微信API直接调用成功，结果: {}", JSONUtil.toJsonStr(result));
            return result;
        } catch (Exception e) {
            log.error("直接调用微信API失败", e);
            throw e;
        }
    }

    @Override
    public ResultData<String> updateUserInfo(UserInfoVo vo) {
        UserInfo userInfo = userInfoRepository.getById(vo.getUserId());
        if (userInfo == null) {
            return ResultData.fail("用户不存在");
        }
        UserInfo info = new UserInfo();
        info.setId(vo.getUserId());
        info.setNickname(vo.getNickname());
        info.setAvatarUrl(vo.getAvatarUrl());
        info.setUpdateTime(new Date());
        userInfoRepository.updateById(info);

        return ResultData.success();
    }

    @Override
    public ResultData<UserInfoVo> checkLoginStatus(Long userId) {
        log.info("校验用户{}登录状态", userId);

        // 1. 参数校验
        if (userId == null) {
            return ResultData.fail("用户ID不能为空");
        }

        // 2. 首先检查用户是否存在
        UserInfo userInfo = userInfoRepository.getById(userId);
        if (userInfo == null) {
            log.info("用户{}不存在，登录状态校验失败", userId);
            return ResultData.fail("用户不存在，请先注册");
        }

        // 3. 尝试从Redis缓存中获取用户登录状态
//        try {
//            String cachedUserInfo = redisUtil.getUserLoginStatus(userId);
//            if (StrUtil.isNotBlank(cachedUserInfo)) {
//                // Redis中存在用户信息，说明用户已登录
//                UserInfoVo userInfoVo = JSONUtil.toBean(cachedUserInfo, UserInfoVo.class);
//                log.info("用户{}登录状态校验成功（从Redis缓存获取）", userId);
//
//                // 延长缓存过期时间
//                redisUtil.extendUserLoginStatus(userId, 7 * 24 * 60 * 60); // 延长7天
//
//                return ResultData.success(userInfoVo);
//            }
//        } catch (Exception e) {
//            log.warn("从Redis获取用户{}登录状态失败，将使用数据库校验", userId, e);
//        }
//
//        // 4. Redis中没有找到或Redis不可用，使用数据库校验（备用方案）
//        // 检查用户状态和最后登录时间
//        if (userInfo.getStatus() != null && userInfo.getStatus() != 0) {
//            log.info("用户{}状态异常（状态：{}），登录状态校验失败", userId, userInfo.getStatus());
//            return ResultData.fail("用户状态异常，请联系管理员");
//        }
//
//        // 检查最后登录时间是否在合理范围内（7天内）
//        Date lastLoginTime = userInfo.getLastLoginTime();
//        if (lastLoginTime == null) {
//            log.info("用户{}从未登录，登录状态校验失败", userId);
//            return ResultData.fail("用户未登录，请先登录");
//        }
//
//        long daysSinceLastLogin = (System.currentTimeMillis() - lastLoginTime.getTime()) / (24 * 60 * 60 * 1000);
//        if (daysSinceLastLogin > 7) {
//            log.info("用户{}最后登录时间超过7天（{}天前），登录状态校验失败", userId, daysSinceLastLogin);
//            return ResultData.fail("登录状态已过期，请重新登录");
//        }
//
//        // 5. 数据库校验通过，构建用户信息并重新缓存到Redis
//        UserInfoVo userInfoVo = new UserInfoVo();
//        BeanUtils.copyProperties(userInfo, userInfoVo);
//
//        // 重新缓存到Redis
//        try {
//            String userInfoJson = JSONUtil.toJsonStr(userInfoVo);
//            redisUtil.setUserLoginStatus(userId, userInfoJson, 7 * 24 * 60 * 60); // 7天过期
//            log.info("用户{}登录状态校验成功（从数据库获取并重新缓存）", userId);
//        } catch (Exception e) {
//            log.warn("重新缓存用户{}登录状态到Redis失败", userId, e);
//        }

        return ResultData.success(null);
    }

}
