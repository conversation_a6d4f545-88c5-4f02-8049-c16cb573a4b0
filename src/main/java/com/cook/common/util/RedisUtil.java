package com.cook.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description Redis工具类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/8/7
 */
@Slf4j
@Component
public class RedisUtil {

    @Autowired(required = false)
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 用户登录状态缓存key前缀
     */
    private static final String USER_LOGIN_PREFIX = "user:login:";

    /**
     * 默认过期时间（7天）
     */
    private static final long DEFAULT_EXPIRE_TIME = 7 * 24 * 60 * 60;

    /**
     * 检查Redis是否可用
     */
    public boolean isRedisAvailable() {
        return stringRedisTemplate != null;
    }

    /**
     * 设置用户登录状态到Redis
     * @param userId 用户ID
     * @param userInfo 用户信息JSON字符串
     * @param expireSeconds 过期时间（秒）
     */
    public void setUserLoginStatus(Long userId, String userInfo, long expireSeconds) {
        if (!isRedisAvailable()) {
            log.warn("Redis不可用，无法设置用户登录状态");
            return;
        }
        
        try {
            String key = USER_LOGIN_PREFIX + userId;
            stringRedisTemplate.opsForValue().set(key, userInfo, expireSeconds, TimeUnit.SECONDS);
            log.debug("设置用户{}登录状态成功，过期时间{}秒", userId, expireSeconds);
        } catch (Exception e) {
            log.error("设置用户{}登录状态失败", userId, e);
        }
    }

    /**
     * 设置用户登录状态到Redis（使用默认过期时间）
     * @param userId 用户ID
     * @param userInfo 用户信息JSON字符串
     */
    public void setUserLoginStatus(Long userId, String userInfo) {
        setUserLoginStatus(userId, userInfo, DEFAULT_EXPIRE_TIME);
    }

    /**
     * 从Redis获取用户登录状态
     * @param userId 用户ID
     * @return 用户信息JSON字符串，如果不存在或Redis不可用则返回null
     */
    public String getUserLoginStatus(Long userId) {
        if (!isRedisAvailable()) {
            log.warn("Redis不可用，无法获取用户登录状态");
            return null;
        }
        
        try {
            String key = USER_LOGIN_PREFIX + userId;
            String userInfo = stringRedisTemplate.opsForValue().get(key);
            log.debug("获取用户{}登录状态: {}", userId, StrUtil.isNotBlank(userInfo) ? "存在" : "不存在");
            return userInfo;
        } catch (Exception e) {
            log.error("获取用户{}登录状态失败", userId, e);
            return null;
        }
    }

    /**
     * 删除用户登录状态
     * @param userId 用户ID
     */
    public void removeUserLoginStatus(Long userId) {
        if (!isRedisAvailable()) {
            log.warn("Redis不可用，无法删除用户登录状态");
            return;
        }
        
        try {
            String key = USER_LOGIN_PREFIX + userId;
            stringRedisTemplate.delete(key);
            log.debug("删除用户{}登录状态成功", userId);
        } catch (Exception e) {
            log.error("删除用户{}登录状态失败", userId, e);
        }
    }

    /**
     * 检查用户是否已登录
     * @param userId 用户ID
     * @return true-已登录，false-未登录或Redis不可用
     */
    public boolean isUserLoggedIn(Long userId) {
        String userInfo = getUserLoginStatus(userId);
        return StrUtil.isNotBlank(userInfo);
    }

    /**
     * 延长用户登录状态过期时间
     * @param userId 用户ID
     * @param expireSeconds 新的过期时间（秒）
     */
    public void extendUserLoginStatus(Long userId, long expireSeconds) {
        if (!isRedisAvailable()) {
            log.warn("Redis不可用，无法延长用户登录状态");
            return;
        }
        
        try {
            String key = USER_LOGIN_PREFIX + userId;
            stringRedisTemplate.expire(key, expireSeconds, TimeUnit.SECONDS);
            log.debug("延长用户{}登录状态过期时间成功，新过期时间{}秒", userId, expireSeconds);
        } catch (Exception e) {
            log.error("延长用户{}登录状态过期时间失败", userId, e);
        }
    }
}
