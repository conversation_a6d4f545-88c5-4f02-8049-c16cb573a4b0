
server:
  port: 8082
  tomcat:
    uri-encoding: UTF-8
spring:
  application:
    name: cook
    jackson:
      default-property-inclusion: non_null
      serialization:
        fail-on-empty-beans: false
  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      # 单个文件最大大小
      max-file-size: 10MB
      # 总上传数据最大大小
      max-request-size: 50MB
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 临时文件存储位置
      location: /tmp
  datasource:
    url: **************************************
    username: root
    password: 101725?zhu?LAN
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: **************
    database: 0
    port: 6379
    password: 1234567890
    username: acman
    connect-timeout: 10s
    timeout: 5s
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms


logging:
  config: classpath:config/logback-config.xml
  level:
    com:
      cook:
        service: debug
        # MyBatis SQL日志
        model:
          mapper: debug
    # MyBatis-Plus SQL日志
    "com.baomidou.mybatisplus": debug
    # MyBatis原生SQL日志
    "org.apache.ibatis": debug
    # 数据库连接池日志
    "com.zaxxer.hikari": info
management:
  metrics:
    tags:
      application: ${spring.application.name}
    export:
      simple:
        enabled: false
  endpoints:
    web:
      exposure:
        include: "health,metrics,prometheus,cache"
  endpoint:
    caches:
      enabled: true
    prometheus:
      enabled: true
    metrics:
      enabled: true

sa-token:
  token-name: satoken
  activity-timeout: -1
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 259200
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  token-prefix: satoken

baidu:
  api:
    key: 2Ng548nNiihgw7yaVZ9LMJWv
    secret: z7spVyVChJDtcIhjmr0x2cKb7gM0ilqt
  ingredient:
    # 返回结果top数，默认为10
    top-num: 10
    # 最小置信度阈值，默认为0.5
    min-score: 0.5
    # 是否使用Feign客户端，false则使用HttpUtil
    use-feign: false
  oss:
    # 百度云OSS配置 - 请确保这些密钥有效且有正确权限
    secret-key-id: ALTAK8Yqy2Ijc2HcSE46i4fIzX
    access-key-secret: a4fe06f144204a419ef79156497826f4
    endpoint: https://version-cook.bj.bcebos.com
    bucket-name: version-cook

# 菜谱导入配置
recipe:
  import:
    # HowToCook项目的dishes文件夹路径
    base-path: /Users/<USER>/Desktop/HowToCook-master/dishes

wx:
  miniapp:
    appid: wxbe4f9d4bae7148d2
    secret: efb3ff0e65fa5224536aa0d1341fa2be
    # 是否使用Feign客户端，false则使用RestTemplate
    use-feign: false

jwt:
  # JWT密钥，生产环境请使用更复杂的密钥
  secret: cook_jwt_secret_key_2025_very_long_secret_key_for_security_please_change_in_production
  # JWT过期时间（毫秒）7天 = 7 * 24 * 60 * 60 * 1000
  expiration: 604800000
  # JWT签发者
  issuer: cook-app
  # JWT请求头名称
  header: Authorization
  # JWT token前缀
  token-prefix: "Bearer "
mybatis-plus:
  configuration:
    # 使用SLF4J日志实现，这样SQL日志会通过logback输出到文件
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 开启懒加载
    lazy-loading-enabled: true
    # 开启积极懒加载
    aggressive-lazy-loading: false
  # 实体类包路径
  type-aliases-package: com.cook.model
  # Mapper XML文件路径
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    db-config:
      # 主键策略：自增
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0

