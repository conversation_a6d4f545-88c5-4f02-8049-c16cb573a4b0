package com.cook.service;

import cn.hutool.json.JSONUtil;
import com.cook.model.base.ResultData;
import com.cook.model.vo.UserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @description 用户登录状态校验测试
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/8/7
 */
@Slf4j
@SpringBootTest
public class UserLoginStatusTest {

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 测试用户登录状态校验 - 用户存在且已登录
     */
    @Test
    public void testCheckLoginStatus_UserExists() {
        // 使用一个存在的用户ID进行测试
        Long userId = 1L;
        
        ResultData<UserInfoVo> result = userInfoService.checkLoginStatus(userId);
        
        log.info("用户{}登录状态校验结果：{}", userId, JSONUtil.toJsonStr(result));
        
        if (result.isSuccess()) {
            log.info("用户{}已登录，用户信息：{}", userId, JSONUtil.toJsonStr(result.getData()));
        } else {
            log.info("用户{}未登录或登录已过期，错误信息：{}", userId, result.getMsg());
        }
    }

    /**
     * 测试用户登录状态校验 - 用户不存在
     */
    @Test
    public void testCheckLoginStatus_UserNotExists() {
        // 使用一个不存在的用户ID进行测试
        Long userId = 99999L;
        
        ResultData<UserInfoVo> result = userInfoService.checkLoginStatus(userId);
        
        log.info("用户{}登录状态校验结果：{}", userId, JSONUtil.toJsonStr(result));
        
        if (result.isSuccess()) {
            log.info("用户{}已登录，用户信息：{}", userId, JSONUtil.toJsonStr(result.getData()));
        } else {
            log.info("用户{}不存在或未登录，错误信息：{}", userId, result.getMsg());
        }
    }

    /**
     * 测试用户登录状态校验 - 参数为空
     */
    @Test
    public void testCheckLoginStatus_NullUserId() {
        Long userId = null;
        
        ResultData<UserInfoVo> result = userInfoService.checkLoginStatus(userId);
        
        log.info("空用户ID登录状态校验结果：{}", JSONUtil.toJsonStr(result));
        
        if (result.isSuccess()) {
            log.info("校验成功，用户信息：{}", JSONUtil.toJsonStr(result.getData()));
        } else {
            log.info("校验失败，错误信息：{}", result.getMsg());
        }
    }
}
