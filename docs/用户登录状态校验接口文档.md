# 用户登录状态校验接口文档

## 接口概述

该接口用于校验指定用户的登录状态，支持Redis缓存和数据库双重校验机制，确保在各种环境下都能正常工作。

## 接口信息

- **接口名称**: 校验用户登录状态
- **请求方法**: `GET`
- **接口路径**: `/user/check-login/{userId}`
- **接口描述**: 根据用户ID校验当前用户是否已经登录

## 请求参数

### 路径参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| userId | Long | 是 | 用户ID | 1001 |

### 请求示例

```http
GET /user/check-login/1001
Host: localhost:8082
Content-Type: application/json
```

```bash
curl -X GET "http://localhost:8082/user/check-login/1001"
```

## 响应参数

### 响应格式

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1001,
    "nickname": "用户昵称",
    "avatarUrl": "https://example.com/avatar.jpg",
    "gender": 1,
    "country": "中国",
    "province": "广东省",
    "city": "深圳市",
    "language": "zh_CN",
    "phone": "13800138000",
    "status": 0,
    "lastLoginTime": "2025-08-07T10:30:00",
    "createTime": "2025-01-01T00:00:00"
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 用户信息对象（登录成功时返回） |

### UserInfoVo字段说明

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| id | Long | 用户ID | 1001 |
| nickname | String | 用户昵称 | "张三" |
| avatarUrl | String | 用户头像URL | "https://example.com/avatar.jpg" |
| gender | Integer | 性别：0-未知，1-男，2-女 | 1 |
| country | String | 国家 | "中国" |
| province | String | 省份 | "广东省" |
| city | String | 城市 | "深圳市" |
| language | String | 语言 | "zh_CN" |
| phone | String | 手机号 | "13800138000" |
| status | Integer | 用户状态：0-正常，1-禁用 | 0 |
| lastLoginTime | String | 最后登录时间 | "2025-08-07T10:30:00" |
| createTime | String | 创建时间 | "2025-01-01T00:00:00" |

## 响应示例

### 成功响应（用户已登录）

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1001,
    "nickname": "张三",
    "avatarUrl": "https://example.com/avatar.jpg",
    "gender": 1,
    "country": "中国",
    "province": "广东省",
    "city": "深圳市",
    "language": "zh_CN",
    "phone": "13800138000",
    "status": 0,
    "lastLoginTime": "2025-08-07T10:30:00",
    "createTime": "2025-01-01T00:00:00"
  }
}
```

### 失败响应（用户不存在）

```json
{
  "code": 500,
  "msg": "用户不存在，请先注册",
  "data": null
}
```

### 失败响应（用户未登录）

```json
{
  "code": 500,
  "msg": "用户未登录，请先登录",
  "data": null
}
```

### 失败响应（登录已过期）

```json
{
  "code": 500,
  "msg": "登录状态已过期，请重新登录",
  "data": null
}
```

### 失败响应（用户状态异常）

```json
{
  "code": 500,
  "msg": "用户状态异常，请联系管理员",
  "data": null
}
```

### 失败响应（参数错误）

```json
{
  "code": 500,
  "msg": "用户ID不能为空",
  "data": null
}
```

## 业务逻辑说明

### 校验流程

1. **参数校验**: 检查userId是否为空
2. **用户存在性校验**: 从数据库查询用户是否存在
3. **Redis缓存校验**: 优先从Redis缓存中获取用户登录状态
4. **数据库备用校验**: 如果Redis不可用或缓存中没有数据，则使用数据库校验
5. **状态校验**: 检查用户状态是否正常
6. **时间校验**: 检查最后登录时间是否在7天内
7. **缓存更新**: 校验成功后更新Redis缓存

### 缓存策略

- **缓存Key格式**: `user:login:{userId}`
- **缓存过期时间**: 7天（604800秒）
- **缓存内容**: 用户信息JSON字符串
- **缓存更新**: 每次校验成功后自动延长过期时间

### 容错机制

- **Redis不可用**: 自动降级到数据库校验
- **缓存失效**: 从数据库重新获取并更新缓存
- **异常处理**: 详细的错误日志记录

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功，用户已登录 |
| 500 | 请求失败，具体错误信息见msg字段 |

## 注意事项

1. **登录有效期**: 用户登录状态有效期为7天，超过7天需要重新登录
2. **缓存依赖**: 接口优先使用Redis缓存，提高响应速度
3. **降级策略**: Redis不可用时自动降级到数据库校验
4. **用户状态**: 只有状态为0（正常）的用户才能通过校验
5. **并发安全**: 接口支持高并发访问

## 错误处理

- 用户不存在时返回明确的错误信息
- 登录过期时提示用户重新登录
- 用户状态异常时建议联系管理员
- 系统异常时记录详细日志便于排查

## 性能说明

- **响应时间**: Redis缓存命中时 < 50ms，数据库查询时 < 200ms
- **并发能力**: 支持高并发访问
- **缓存命中率**: 正常情况下缓存命中率 > 90%
