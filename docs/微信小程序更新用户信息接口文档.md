# 微信小程序更新用户信息接口文档

## 接口概述

该接口用于微信小程序用户更新个人信息，支持更新用户昵称、头像等基本信息。

## 接口信息

- **接口名称**: 微信小程序更新用户信息
- **请求方法**: `POST`
- **接口路径**: `/user/wx-update`
- **Content-Type**: `application/json`
- **接口描述**: 更新微信小程序用户的个人信息

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| Content-Type | String | 是 | 请求内容类型 | application/json |

### 请求体参数 (UserInfoVo)

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 用户ID | 1001 |
| nickname | String | 否 | 用户昵称 | "张三" |
| avatarUrl | String | 否 | 用户头像URL | "https://example.com/avatar.jpg" |
| gender | Integer | 否 | 性别：0-未知，1-男，2-女 | 1 |
| country | String | 否 | 国家 | "中国" |
| province | String | 否 | 省份 | "广东省" |
| city | String | 否 | 城市 | "深圳市" |
| language | String | 否 | 语言 | "zh_CN" |
| phone | String | 否 | 手机号 | "13800138000" |
| status | Integer | 否 | 用户状态：0-正常，1-禁用 | 0 |

### 请求示例

```http
POST /user/wx-update
Host: localhost:8082
Content-Type: application/json

{
  "userId": 1001,
  "nickname": "新昵称",
  "avatarUrl": "https://example.com/new-avatar.jpg"
}
```

```bash
curl -X POST "http://localhost:8082/user/wx-update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1001,
    "nickname": "新昵称",
    "avatarUrl": "https://example.com/new-avatar.jpg"
  }'
```

```javascript
// JavaScript 示例
const updateUserInfo = async (userInfo) => {
  try {
    const response = await fetch('/user/wx-update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userInfo)
    });
    
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('更新用户信息失败:', error);
    throw error;
  }
};

// 使用示例
updateUserInfo({
  id: 1001,
  nickname: "新昵称",
  avatarUrl: "https://example.com/new-avatar.jpg"
});
```

## 响应参数

### 响应格式

```json
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| msg | String | 响应消息 |
| data | String | 响应数据，更新成功时通常为null |

## 响应示例

### 成功响应

```json
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

### 失败响应（用户不存在）

```json
{
  "code": 500,
  "msg": "用户不存在",
  "data": null
}
```

### 失败响应（参数错误）

```json
{
  "code": 500,
  "msg": "用户ID不能为空",
  "data": null
}
```

### 失败响应（系统异常）

```json
{
  "code": 500,
  "msg": "系统异常，请稍后重试",
  "data": null
}
```

## 业务逻辑说明

### 更新流程

1. **参数校验**: 检查用户ID是否存在
2. **用户验证**: 验证用户是否存在于数据库中
3. **信息更新**: 更新用户的昵称、头像等信息
4. **时间更新**: 自动更新用户信息的修改时间
5. **返回结果**: 返回更新操作的结果

### 更新字段

当前接口主要更新以下字段：
- **nickname**: 用户昵称
- **avatarUrl**: 用户头像URL
- **updateTime**: 更新时间（自动设置）

### 数据验证

- 用户ID必须存在且有效
- 用户必须在数据库中存在
- 其他字段为可选更新

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功，用户信息更新成功 |
| 500 | 请求失败，具体错误信息见msg字段 |

## 注意事项

1. **用户ID必填**: id字段是必填的，用于标识要更新的用户
2. **部分更新**: 只需要传入需要更新的字段，不需要传入所有字段
3. **数据安全**: 接口会验证用户是否存在，防止无效更新
4. **时间自动更新**: updateTime字段会自动设置为当前时间
5. **幂等性**: 多次调用相同参数的更新操作结果一致

## 错误处理

- **用户不存在**: 当传入的用户ID在数据库中不存在时返回错误
- **参数验证**: 对必填参数进行验证
- **系统异常**: 捕获并处理数据库操作异常

## 使用场景

1. **用户资料编辑**: 用户在小程序中编辑个人资料
2. **头像更新**: 用户更换头像后调用此接口
3. **昵称修改**: 用户修改昵称时使用
4. **批量信息更新**: 一次性更新多个用户信息字段

## 性能说明

- **响应时间**: 通常 < 200ms
- **并发能力**: 支持中等并发访问
- **数据库操作**: 单次UPDATE操作，性能良好

## 安全建议

1. **权限验证**: 建议添加用户身份验证，确保只能更新自己的信息
2. **参数过滤**: 对输入参数进行安全过滤，防止XSS攻击
3. **频率限制**: 可以考虑添加更新频率限制，防止恶意频繁更新
4. **敏感信息**: 避免通过此接口更新敏感信息如手机号等

## 扩展建议

1. **字段扩展**: 可以根据业务需要扩展更多可更新字段
2. **批量更新**: 可以扩展支持批量更新多个用户信息
3. **版本控制**: 可以添加数据版本控制，防止并发更新冲突
4. **审计日志**: 可以添加用户信息变更的审计日志
